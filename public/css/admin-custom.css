/* Hide license warning messages - more specific selectors */
.alert-license,
[data-bb-toggle="authorized-reminder"] {
    display: none !important;
}

/* Hide license form sections */
#license-form,
form#license-form {
    display: none !important;
}

/* Hide license activation modal */
#quick-activation-license-modal {
    display: none !important;
}

/* Hide specific license warnings by content */
.alert-warning.bg-warning.text-white:contains("Your license is invalid"),
.alert.alert-warning.bg-warning.text-white:contains("license"),
.d-flex.justify-content-between.align-items-center:contains("Your license is invalid") {
    display: none !important;
}

/* Hide license section in settings only */
.row.mb-5.d-block.d-md-flex:has(#license-form) {
    display: none !important;
}

/* Hide license settings section specifically */
.col-12.col-md-3:has(h2:contains("License")) {
    display: none !important;
}

.col-12.col-md-3:has(h2:contains("License")) + .col-12.col-md-9 {
    display: none !important;
}
