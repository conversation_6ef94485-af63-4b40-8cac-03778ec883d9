/* Hide license warning messages */
.alert-license,
[data-bb-toggle="authorized-reminder"],
.alert-warning.bg-warning.text-white,
.alert-warning,
.alert.alert-warning.bg-warning.text-white {
    display: none !important;
}

/* Hide license form sections */
#license-form,
form#license-form,
.card:has(#license-form),
.row:has(#license-form),
.row.mb-5.d-block.d-md-flex {
    display: none !important;
}

/* Hide license activation modal */
#quick-activation-license-modal {
    display: none !important;
}

/* Hide license sections in settings by targeting specific elements */
h2:contains("License"),
h2:contains("license"),
.col-12.col-md-3:has(h2:contains("License")),
.col-12.col-md-9:has(#license-form) {
    display: none !important;
}

/* Hide elements containing specific license text */
*:contains("Your license is invalid"),
*:contains("Please activate your license"),
*:contains("Setup license code"),
*:contains("License Activation"),
*:contains("Activate license") {
    display: none !important;
}

/* Hide warning alerts completely */
.alert[role="alert"] {
    display: none !important;
}

/* Hide any div with license-related content */
div[class*="alert"],
div[class*="warning"] {
    display: none !important;
}

/* Hide specific license warning structure */
.d-flex.justify-content-between.align-items-center {
    display: none !important;
}

/* Hide license-related cards */
.card .card-body:has(.alert-warning) {
    display: none !important;
}

.card:has(.alert-warning) {
    display: none !important;
}
